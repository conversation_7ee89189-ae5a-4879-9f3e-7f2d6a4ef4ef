'use client';

import { Caption } from '@telegram-apps/telegram-ui';

import { CollectionName } from '@/components/shared/collection-name';
import { OrderImage } from '@/components/shared/order-image';
import { Card, CardContent } from '@/components/ui/card';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';

interface BaseOrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
  animated?: boolean;
  children: React.ReactNode;
}

export function BaseOrderCard({
  order,
  collection,
  onClick,
  children,
}: BaseOrderCardProps) {
  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col">
        <OrderImage order={order} collection={collection} />

        <div className="flex items-center justify-between mt-2 mb-2">
          <Caption level="1" weight="1" className="truncate">
            <CollectionName collection={collection} />
          </Caption>
          <Caption level="2" weight="3" className="w-fit text-[#78797e]">
            #
            {order.number ??
              (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
          </Caption>
        </div>

        {children}
      </CardContent>
    </Card>
  );
}
