import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import {
  APP_USERS_COLLECTION,
  AppCloudFunctions,
  type UserBalance,
  type UserEntity,
} from '@/constants/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface UpdateUserData {
  id?: string;
  name?: string;
  displayName?: string;
  email?: string;
  photoURL?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string;
  referral_fee?: number;
  balance?: UserBalance;
  role?: 'admin' | 'user';
}

interface CloudFunctionResponse {
  success: boolean;
  message: string;
  updatedFields: string[];
}

export const updateUser = async (
  userId: string,
  updateData: UpdateUserData,
) => {
  try {
    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User not authenticated with Firebase Auth');
    }

    const changeUserData = httpsCallable(
      firebaseFunctions,
      AppCloudFunctions.changeUserData,
    );

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const cloudFunctionData: any = {
      userId: userId,
    };

    if (updateData.displayName !== undefined) {
      cloudFunctionData.name = updateData.displayName;
    }
    if (updateData.tg_id !== undefined) {
      cloudFunctionData.tg_id = updateData.tg_id;
    }
    if (updateData.ton_wallet_address !== undefined) {
      cloudFunctionData.ton_wallet_address = updateData.ton_wallet_address;
    }
    if (updateData.raw_ton_wallet_address !== undefined) {
      cloudFunctionData.raw_ton_wallet_address =
        updateData.raw_ton_wallet_address;
    }
    if (updateData.referrer_id !== undefined) {
      cloudFunctionData.referrer_id = updateData.referrer_id;
    }

    const result = await changeUserData(cloudFunctionData);
    const response = result.data as CloudFunctionResponse;

    if (!response.success) {
      throw new Error(response.message || 'Failed to update user');
    }

    const updatedUser = await getUserById(userId);
    if (!updatedUser) {
      throw new Error(`User with ID ${userId} not found after update`);
    }

    return updatedUser;
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error);
    throw error;
  }
};

export const getUserById = async (userId: string) => {
  try {
    const userRef = doc(firestore, APP_USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return null;
    }

    return {
      id: userDoc.id,
      ...userDoc.data(),
    } as UserEntity;
  } catch (error) {
    console.error(`Error getting user ${userId}:`, error);
    throw error;
  }
};

export const searchUsersByTelegramHandle = async (searchTerm: string) => {
  try {
    if (!searchTerm.trim()) {
      return [];
    }

    const q = query(
      collection(firestore, APP_USERS_COLLECTION),
      where('telegram_handle', '==', searchTerm),
    );

    const snapshot = await getDocs(q);
    const users: UserEntity[] = [];

    snapshot.forEach((doc) => {
      users.push({ id: doc.id, ...doc.data() } as UserEntity);
    });

    return users;
  } catch (error) {
    console.error('Error searching users by telegram handle:', error);
    throw error;
  }
};

export const updateUserWallet = async (
  userId: string,
  tonWalletAddress: string,
  rawTonWalletAddress?: string,
) => {
  try {
    const updateData: UpdateUserData = {
      ton_wallet_address: tonWalletAddress,
    };

    if (rawTonWalletAddress !== undefined) {
      updateData.raw_ton_wallet_address = rawTonWalletAddress;
    }

    return await updateUser(userId, updateData);
  } catch (error) {
    console.error(`Error updating wallet for user ${userId}:`, error);
    throw error;
  }
};

export const getUsersWithCustomReferralFees = async () => {
  try {
    const usersRef = collection(firestore, APP_USERS_COLLECTION);
    const q = query(usersRef, where('referral_fee', '!=', null));
    const querySnapshot = await getDocs(q);

    const users: UserEntity[] = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data() as UserEntity;
      if (userData.referral_fee !== undefined && userData.referral_fee > 0) {
        users.push({ ...userData, id: doc.id });
      }
    });

    return users;
  } catch (error) {
    console.error('Error getting users with custom referral fees:', error);
    throw error;
  }
};

export const getUserReferrals = async (userId: string) => {
  try {
    const usersRef = collection(firestore, APP_USERS_COLLECTION);
    const q = query(usersRef, where('referrer_id', '==', userId));
    const querySnapshot = await getDocs(q);

    const referrals: UserEntity[] = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data() as UserEntity;
      referrals.push({ ...userData, id: doc.id });
    });

    return referrals;
  } catch (error) {
    console.error('Error getting user referrals:', error);
    throw error;
  }
};

export const getUsersByIds = async (
  userIds: string[],
): Promise<UserEntity[]> => {
  try {
    if (userIds.length === 0) {
      return [];
    }

    // Firestore 'in' queries are limited to 10 items, so we need to batch
    const batchSize = 10;
    const batches: Promise<UserEntity[]>[] = [];

    for (let i = 0; i < userIds.length; i += batchSize) {
      const batchIds = userIds.slice(i, i + batchSize);
      const batchPromise = getBatchUsers(batchIds);
      batches.push(batchPromise);
    }

    const batchResults = await Promise.all(batches);
    return batchResults.flat();
  } catch (error) {
    console.error('Error getting users by IDs:', error);
    throw error;
  }
};

const getBatchUsers = async (userIds: string[]): Promise<UserEntity[]> => {
  const usersRef = collection(firestore, APP_USERS_COLLECTION);
  const q = query(usersRef, where('__name__', 'in', userIds));
  const querySnapshot = await getDocs(q);

  const users: UserEntity[] = [];
  querySnapshot.forEach((doc) => {
    const userData = doc.data() as UserEntity;
    users.push({ ...userData, id: doc.id });
  });

  return users;
};
