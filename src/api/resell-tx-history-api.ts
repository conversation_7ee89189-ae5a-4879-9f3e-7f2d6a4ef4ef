import {
  collection,
  getDocs,
  orderBy,
  query,
  where,
} from 'firebase/firestore';

import {
  RESELL_TX_HISTORY_COLLECTION_NAME,
  type ResellTxHistoryEntity,
  type UserEntity,
} from '@/constants/core.constants';
import { firestore } from '@/root-context';

import { getUsersByIds } from './user-api';

export interface ResellTxHistoryWithUsers {
  id: string;
  order_id: string;
  execution_price: string;
  reseller_id: string;
  executed_at: Date;
  buyer_id: string;
  reseller?: UserEntity;
  buyer?: UserEntity;
}

export interface ResellHistoryResponse {
  success: boolean;
  transactions: ResellTxHistoryWithUsers[];
  message?: string;
}

export const getResellHistoryByOrderId = async (
  orderId: string,
): Promise<ResellHistoryResponse> => {
  try {
    if (!orderId) {
      return {
        success: false,
        transactions: [],
        message: 'Order ID is required',
      };
    }

    // Query resell transaction history for the specific order
    const q = query(
      collection(firestore, RESELL_TX_HISTORY_COLLECTION_NAME),
      where('order_id', '==', orderId),
      orderBy('executed_at', 'desc'),
    );

    const snapshot = await getDocs(q);
    const transactions: ResellTxHistoryEntity[] = [];

    snapshot.forEach((doc) => {
      const data = doc.data() as Omit<ResellTxHistoryEntity, 'id'>;
      transactions.push({
        id: doc.id,
        ...data,
        executed_at: data.executed_at?.toDate?.() || new Date(),
      });
    });

    if (transactions.length === 0) {
      return {
        success: true,
        transactions: [],
        message: 'No resell history found for this order',
      };
    }

    // Extract unique user IDs for batch fetching
    const userIds = new Set<string>();
    transactions.forEach((tx) => {
      userIds.add(tx.reseller_id);
      userIds.add(tx.buyer_id);
    });

    // Batch fetch user details
    const users = await getUsersByIds(Array.from(userIds));
    const userMap = new Map<string, UserEntity>();
    users.forEach((user) => {
      userMap.set(user.id, user);
    });

    // Combine transaction data with user details
    const transactionsWithUsers: ResellTxHistoryWithUsers[] = transactions.map(
      (tx) => ({
        ...tx,
        reseller: userMap.get(tx.reseller_id),
        buyer: userMap.get(tx.buyer_id),
      }),
    );

    return {
      success: true,
      transactions: transactionsWithUsers,
    };
  } catch (error) {
    console.error('Error fetching resell history:', error);
    return {
      success: false,
      transactions: [],
      message: 'Failed to fetch resell history',
    };
  }
};
