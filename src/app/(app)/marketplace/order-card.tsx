'use client';

import { BaseOrderCard } from '@/components/shared/base-order-card';
import { PriceButton, PriceRow } from '@/components/shared/price-display';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

interface OrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
}

export function OrderCard({ order, collection, onClick }: OrderCardProps) {
  const isSecondary = isSecondaryMarketOrder(order);

  return (
    <BaseOrderCard order={order} collection={collection} onClick={onClick}>
      {isSecondary && (
        <div className="mb-2">
          <SecondaryMarketBadge />
        </div>
      )}

      {isSecondary ? (
        <>
          <div className="space-y-1 mb-2">
            <PriceRow
              label="Primary:"
              amount={order.price}
              className="text-[#708499]"
            />
            <PriceRow
              label="Secondary:"
              amount={order.secondaryMarketPrice || 0}
              className="text-[#6ab2f2]"
              tonLogoClassName="text-[#6ab2f2]"
            />
          </div>
          <PriceButton amount={order.secondaryMarketPrice || 0} />
        </>
      ) : (
        <PriceButton amount={order.price} />
      )}
    </BaseOrderCard>
  );
}
